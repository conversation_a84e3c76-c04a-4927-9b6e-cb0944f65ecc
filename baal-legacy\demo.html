<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>巴尔的遗产 - 演示版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d1810 100%);
            color: #e0e0e0;
            height: 100vh;
            overflow: hidden;
        }

        .app {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .game-header {
            height: 80px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d1810 100%);
            border-bottom: 3px solid #8B4513;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
        }

        .game-title {
            font-size: 24px;
            font-weight: bold;
            color: #FFD700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            letter-spacing: 1px;
        }

        .gold-display {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 215, 0, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            border: 1px solid #FFD700;
        }

        .gold-amount {
            color: #FFD700;
            font-weight: bold;
            font-size: 16px;
            text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
        }

        .game-layout {
            flex: 1;
            display: grid;
            grid-template-columns: 300px 1fr 300px;
            gap: 10px;
            padding: 10px;
        }

        .panel {
            background: rgba(20, 20, 20, 0.9);
            border: 2px solid #8B4513;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 0 20px rgba(139, 69, 19, 0.3);
        }

        .center-panel {
            background: rgba(15, 15, 15, 0.95);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .panel-title {
            font-size: 16px;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 15px;
            text-align: center;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
            border-bottom: 1px solid #8B4513;
            padding-bottom: 8px;
        }

        .game-button {
            background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
            border: 1px solid #8B4513;
            color: #e0e0e0;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-family: inherit;
            font-size: 14px;
            margin: 10px;
        }

        .game-button:hover {
            background: linear-gradient(145deg, #4a4a4a, #3a3a3a);
            box-shadow: 0 0 10px rgba(139, 69, 19, 0.5);
            transform: translateY(-1px);
        }

        .game-button.primary {
            background: linear-gradient(145deg, #8B4513, #A0522D);
            color: #fff;
        }

        .demo-content {
            max-width: 600px;
            line-height: 1.6;
        }

        .demo-content h2 {
            color: #FFD700;
            margin-bottom: 20px;
        }

        .demo-content p {
            margin-bottom: 15px;
            color: #ccc;
        }

        .features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .feature {
            background: rgba(30, 30, 30, 0.6);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #444;
        }

        .feature h3 {
            color: #CD853F;
            margin-bottom: 10px;
        }

        .status-bar {
            height: 40px;
            background: rgba(10, 10, 10, 0.95);
            border-top: 2px solid #8B4513;
            display: flex;
            align-items: center;
            padding: 0 20px;
            gap: 30px;
            font-size: 12px;
            color: #ccc;
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px rgba(139, 69, 19, 0.5); }
            50% { box-shadow: 0 0 20px rgba(139, 69, 19, 0.8); }
        }

        .glow {
            animation: glow 2s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="app">
        <header class="game-header">
            <div>
                <h1 class="game-title">巴尔的遗产</h1>
                <div style="font-size: 12px; color: #CD853F; font-style: italic;">Baal's Legacy - 演示版</div>
            </div>
            <div class="gold-display">
                <span>💰</span>
                <span class="gold-amount">1,000</span>
            </div>
        </header>

        <div class="game-layout">
            <div class="panel">
                <div class="panel-title">佣兵团队</div>
                <div style="text-align: center; color: #888; font-style: italic; padding: 20px;">
                    <p>还没有佣兵</p>
                    <p>点击"招募佣兵"开始你的冒险！</p>
                    <button class="game-button primary">招募佣兵</button>
                </div>
            </div>

            <div class="panel center-panel">
                <div class="demo-content">
                    <h2>🎮 巴尔的遗产 - 暗黑风格挂机RPG</h2>
                    
                    <p>欢迎来到《巴尔的遗产》！这是一款致敬《暗黑破坏神2》的网页挂机RPG游戏。</p>

                    <div class="features">
                        <div class="feature">
                            <h3>🧙‍♂️ 四大职业</h3>
                            <p>法师、圣骑士、游侠、狂战士，每个职业都有独特的技能和玩法。</p>
                        </div>
                        <div class="feature">
                            <h3>⚔️ 丰富装备</h3>
                            <p>1000+独特装备，随机属性，符文系统，打造你的专属装备。</p>
                        </div>
                        <div class="feature">
                            <h3>👥 佣兵团队</h3>
                            <p>最多3人小队，合理搭配职业，挑战各种强敌。</p>
                        </div>
                        <div class="feature">
                            <h3>🎯 自动挂机</h3>
                            <p>自动战斗、自动拾取、自动升级，轻松享受游戏乐趣。</p>
                        </div>
                    </div>

                    <div style="margin-top: 30px;">
                        <button class="game-button primary glow" onclick="startDemo()">开始游戏</button>
                        <button class="game-button" onclick="showFeatures()">查看更多功能</button>
                    </div>
                </div>
            </div>

            <div class="panel">
                <div class="panel-title">控制面板</div>
                <div style="text-align: center; padding: 20px;">
                    <button class="game-button" style="width: 100%; margin-bottom: 15px;">▶️ 开始挂机</button>
                    <div style="background: rgba(30, 30, 30, 0.6); padding: 15px; border-radius: 8px; border: 1px solid #444;">
                        <div style="color: #FFD700; font-size: 14px; font-weight: bold; margin-bottom: 10px;">神秘商店</div>
                        <div style="font-size: 12px; color: #4169E1; margin-bottom: 5px;">魔法物品 - 50💰</div>
                        <div style="font-size: 12px; color: #FFD700; margin-bottom: 5px;">稀有物品 - 200💰</div>
                        <div style="font-size: 12px; color: #FF4500;">传说物品 - 1000💰</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="status-bar">
            <div>游戏时间: 0:00</div>
            <div>角色数量: 0</div>
            <div>队伍: 0/3</div>
            <div>状态: 演示模式</div>
        </div>
    </div>

    <script>
        function startDemo() {
            alert('🎮 欢迎体验《巴尔的遗产》！\n\n这是一个演示版本，展示了游戏的基本界面和设计风格。\n\n完整版本包含：\n• 完整的角色系统和技能树\n• 丰富的装备和符文系统\n• 多样化的敌人和地图\n• 自动挂机和手动操作模式\n• 数据持久化和进度保存\n\n感谢您的体验！');
        }

        function showFeatures() {
            alert('🌟 游戏特色功能：\n\n📊 深度数值系统\n• 8种基础属性\n• 5个装备稀有度等级\n• 随机属性生成\n\n⚡ 战斗系统\n• 实时战斗计算\n• 技能和光环效果\n• 队伍协作机制\n\n🎒 装备系统\n• 镶嵌孔和符文\n• 装备升级和强化\n• 套装效果\n\n🏪 经济系统\n• 金币交易\n• 神秘商店\n• 装备买卖');
        }

        // 简单的动画效果
        setInterval(() => {
            const goldAmount = document.querySelector('.gold-amount');
            const currentGold = parseInt(goldAmount.textContent.replace(',', ''));
            const newGold = currentGold + Math.floor(Math.random() * 10);
            goldAmount.textContent = newGold.toLocaleString();
        }, 3000);

        // 状态栏时间更新
        let gameTime = 0;
        setInterval(() => {
            gameTime++;
            const minutes = Math.floor(gameTime / 60);
            const seconds = gameTime % 60;
            document.querySelector('.status-bar div').textContent = 
                `游戏时间: ${minutes}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    </script>
</body>
</html>
