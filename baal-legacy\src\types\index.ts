// 游戏基础类型定义

// 职业类型
export enum CharacterClass {
  MAGE = 'mage',
  PALADIN = 'paladin',
  RANGER = 'ranger',
  BARBARIAN = 'barbarian'
}

// 装备类型
export enum ItemType {
  WEAPON = 'weapon',
  ARMOR = 'armor',
  HELMET = 'helmet',
  BOOTS = 'boots',
  GLOVES = 'gloves',
  RING = 'ring',
  AMULET = 'amulet'
}

// 装备品质
export enum ItemRarity {
  NORMAL = 'normal',
  MAGIC = 'magic',
  RARE = 'rare',
  UNIQUE = 'unique',
  LEGENDARY = 'legendary'
}

// 属性类型
export enum StatType {
  STRENGTH = 'strength',
  DEXTERITY = 'dexterity',
  INTELLIGENCE = 'intelligence',
  VITALITY = 'vitality',
  DAMAGE = 'damage',
  DEFENSE = 'defense',
  HEALTH = 'health',
  MANA = 'mana'
}

// 技能类型
export enum SkillType {
  ACTIVE = 'active',
  PASSIVE = 'passive',
  AURA = 'aura'
}

// 基础属性接口
export interface Stats {
  strength: number;
  dexterity: number;
  intelligence: number;
  vitality: number;
  damage: number;
  defense: number;
  health: number;
  mana: number;
}

// 装备属性
export interface ItemAffix {
  type: StatType;
  value: number;
  isPercentage?: boolean;
}

// 装备接口
export interface Item {
  id: string;
  name: string;
  type: ItemType;
  rarity: ItemRarity;
  level: number;
  stats: ItemAffix[];
  sockets?: number;
  socketedRunes?: Rune[];
  description?: string;
  icon?: string;
}

// 符文接口
export interface Rune {
  id: string;
  name: string;
  level: number;
  stats: ItemAffix[];
  description: string;
}

// 技能接口
export interface Skill {
  id: string;
  name: string;
  type: SkillType;
  class: CharacterClass;
  level: number;
  maxLevel: number;
  description: string;
  effects: ItemAffix[];
  manaCost?: number;
  cooldown?: number;
  prerequisites?: string[];
}

// 角色接口
export interface Character {
  id: string;
  name: string;
  class: CharacterClass;
  level: number;
  experience: number;
  experienceToNext: number;
  stats: Stats;
  equipment: Partial<Record<ItemType, Item>>;
  skills: Skill[];
  activeSkills: string[];
  isActive: boolean;
}

// 敌人接口
export interface Enemy {
  id: string;
  name: string;
  level: number;
  health: number;
  maxHealth: number;
  damage: number;
  defense: number;
  experience: number;
  lootTable: LootDrop[];
}

// 掉落物品
export interface LootDrop {
  itemId: string;
  chance: number; // 0-1之间的概率
  quantity?: number;
}

// 地图区域
export interface Area {
  id: string;
  name: string;
  level: number;
  enemies: Enemy[];
  description: string;
  unlocked: boolean;
}

// 战斗结果
export interface BattleResult {
  victory: boolean;
  experience: number;
  loot: Item[];
  damage: number;
}

// 游戏状态
export interface GameState {
  characters: Character[];
  activeParty: string[]; // 最多3个角色ID
  currentArea: string;
  areas: Area[];
  inventory: Item[];
  gold: number;
  isAutoPlaying: boolean;
  gameTime: number;
  totalKills: number;
  totalGoldEarned: number;
  settings: GameSettings;
}

// 游戏设置
export interface GameSettings {
  autoSell: boolean;
  autoEquip: boolean;
  soundEnabled: boolean;
  musicEnabled: boolean;
  gameSpeed: number;
}
