.skill-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.character-selector {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(30, 30, 30, 0.6);
  border-radius: 8px;
  border: 1px solid #444;
}

.character-selector label {
  display: block;
  color: #FFD700;
  margin-bottom: 8px;
  font-weight: bold;
  font-size: 14px;
}

.character-select {
  width: 100%;
  padding: 8px;
  background: rgba(20, 20, 20, 0.8);
  border: 1px solid #8B4513;
  border-radius: 4px;
  color: #e0e0e0;
  font-family: inherit;
  font-size: 12px;
}

.character-select:focus {
  outline: none;
  border-color: #FFD700;
  box-shadow: 0 0 5px rgba(255, 215, 0, 0.3);
}

.skill-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.character-info {
  background: rgba(30, 30, 30, 0.6);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #444;
}

.character-info h3 {
  color: #FFD700;
  margin: 0 0 10px 0;
  font-size: 16px;
}

.character-details {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #ccc;
}

.learned-skills,
.available-skills {
  background: rgba(30, 30, 30, 0.6);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #444;
}

.learned-skills h4,
.available-skills h4 {
  color: #FFD700;
  margin: 0 0 15px 0;
  font-size: 14px;
  border-bottom: 1px solid #8B4513;
  padding-bottom: 8px;
}

.no-skills {
  text-align: center;
  color: #888;
  font-style: italic;
  padding: 20px;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
}

.skill-card {
  background: rgba(20, 20, 20, 0.8);
  border: 2px solid #444;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.3s ease;
  position: relative;
}

.skill-card.learned {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.skill-card.available {
  border-color: #FFD700;
  background: rgba(255, 215, 0, 0.1);
}

.skill-card.available:hover {
  border-color: #FFA500;
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
  transform: translateY(-2px);
}

.skill-card.locked {
  border-color: #666;
  background: rgba(50, 50, 50, 0.3);
  opacity: 0.6;
}

.skill-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.skill-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.skill-name {
  flex: 1;
  font-weight: bold;
  color: #e0e0e0;
  font-size: 14px;
}

.skill-level {
  background: #4CAF50;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
}

.skill-req-level {
  background: #8B4513;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
}

.skill-type {
  color: #CD853F;
  font-size: 11px;
  margin-bottom: 8px;
  font-style: italic;
}

.skill-description {
  color: #ccc;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 10px;
}

.skill-prerequisites {
  margin-bottom: 10px;
  padding: 8px;
  background: rgba(139, 69, 19, 0.2);
  border-radius: 4px;
  border-left: 3px solid #8B4513;
}

.prereq-label {
  color: #FFD700;
  font-size: 11px;
  font-weight: bold;
  margin-bottom: 4px;
}

.prereq-skill {
  color: #CD853F;
  font-size: 10px;
  margin-bottom: 2px;
}

.skill-cost,
.skill-cooldown {
  color: #87CEEB;
  font-size: 11px;
  margin-bottom: 4px;
}

.skill-effects {
  display: flex;
  flex-direction: column;
  gap: 3px;
  margin-bottom: 10px;
}

.effect {
  color: #4CAF50;
  font-size: 11px;
  font-weight: bold;
}

.learn-skill-btn {
  width: 100%;
  padding: 8px;
  background: linear-gradient(145deg, #FFD700, #FFA500);
  border: none;
  border-radius: 4px;
  color: #000;
  font-weight: bold;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.learn-skill-btn:hover {
  background: linear-gradient(145deg, #FFA500, #FFD700);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
}

.no-characters {
  text-align: center;
  color: #888;
  font-style: italic;
  padding: 40px 20px;
}

.no-characters p {
  margin: 5px 0;
}

/* 滚动条样式 */
.skill-content::-webkit-scrollbar {
  width: 8px;
}

.skill-content::-webkit-scrollbar-track {
  background: rgba(20, 20, 20, 0.5);
  border-radius: 4px;
}

.skill-content::-webkit-scrollbar-thumb {
  background: #8B4513;
  border-radius: 4px;
}

.skill-content::-webkit-scrollbar-thumb:hover {
  background: #A0522D;
}

@media (max-width: 900px) {
  .skills-grid {
    grid-template-columns: 1fr;
  }
  
  .character-details {
    flex-direction: column;
    gap: 5px;
  }
  
  .skill-card {
    padding: 10px;
  }
  
  .skill-header {
    flex-wrap: wrap;
  }
}
