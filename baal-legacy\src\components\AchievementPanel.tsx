import React, { useState, useEffect } from 'react';
import { useGameStore } from '../stores/gameStore';
import {
  achievementDatabase,
  checkAchievementProgress,
  claimAchievementReward,
  getAchievementRarityColor,
  formatProgress,
  Achievement
} from '../utils/achievementUtils';
import './AchievementPanel.css';

const AchievementPanel: React.FC = () => {
  const gameState = useGameStore();
  const [achievements, setAchievements] = useState<Achievement[]>(achievementDatabase);
  const [filter, setFilter] = useState<'all' | 'completed' | 'uncompleted'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'progress' | 'rarity'>('progress');

  // 更新成就进度
  useEffect(() => {
    const updatedAchievements = achievements.map(achievement => 
      checkAchievementProgress(achievement, gameState)
    );
    setAchievements(updatedAchievements);
  }, [gameState.characters, gameState.inventory, gameState.gameTime, gameState.areas]);

  // 领取成就奖励
  const handleClaimReward = (achievement: Achievement) => {
    if (!achievement.completed) return;

    const reward = claimAchievementReward(achievement);
    
    if (reward.gold > 0) {
      gameState.addGold(reward.gold);
    }
    
    if (reward.experience > 0) {
      // 给所有队伍成员分配经验
      gameState.activeParty.forEach(characterId => {
        gameState.addExperience(characterId, reward.experience);
      });
    }

    // 标记成就为已领取
    setAchievements(prev => prev.map(a => 
      a.id === achievement.id ? { ...a, claimed: true } : a
    ));
  };

  // 过滤成就
  const filteredAchievements = achievements.filter(achievement => {
    switch (filter) {
      case 'completed':
        return achievement.completed;
      case 'uncompleted':
        return !achievement.completed;
      default:
        return true;
    }
  });

  // 排序成就
  const sortedAchievements = [...filteredAchievements].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'rarity':
        const rarityOrder = { common: 1, rare: 2, epic: 3, legendary: 4 };
        return rarityOrder[b.rarity] - rarityOrder[a.rarity];
      case 'progress':
      default:
        if (a.completed && !b.completed) return -1;
        if (!a.completed && b.completed) return 1;
        const aProgress = a.current / a.target;
        const bProgress = b.current / b.target;
        return bProgress - aProgress;
    }
  });

  const completedCount = achievements.filter(a => a.completed).length;
  const totalCount = achievements.length;
  const completionPercentage = Math.floor((completedCount / totalCount) * 100);

  return (
    <div className="achievement-panel">
      <div className="panel-title">成就系统</div>
      
      <div className="achievement-summary">
        <div className="summary-stats">
          <div className="stat-item">
            <span className="stat-label">完成进度:</span>
            <span className="stat-value">{completedCount}/{totalCount} ({completionPercentage}%)</span>
          </div>
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: `${completionPercentage}%` }}
            />
          </div>
        </div>
      </div>

      <div className="achievement-controls">
        <div className="filter-controls">
          <label>筛选:</label>
          <select 
            value={filter} 
            onChange={(e) => setFilter(e.target.value as any)}
            className="control-select"
          >
            <option value="all">全部</option>
            <option value="completed">已完成</option>
            <option value="uncompleted">未完成</option>
          </select>
        </div>

        <div className="sort-controls">
          <label>排序:</label>
          <select 
            value={sortBy} 
            onChange={(e) => setSortBy(e.target.value as any)}
            className="control-select"
          >
            <option value="progress">进度</option>
            <option value="name">名称</option>
            <option value="rarity">稀有度</option>
          </select>
        </div>
      </div>

      <div className="achievements-list">
        {sortedAchievements.length === 0 ? (
          <div className="no-achievements">没有符合条件的成就</div>
        ) : (
          sortedAchievements.map(achievement => (
            <div 
              key={achievement.id}
              className={`achievement-card ${achievement.completed ? 'completed' : 'incomplete'}`}
              style={{ borderColor: getAchievementRarityColor(achievement.rarity) }}
            >
              <div className="achievement-header">
                <div className="achievement-icon">{achievement.icon}</div>
                <div className="achievement-info">
                  <div 
                    className="achievement-name"
                    style={{ color: getAchievementRarityColor(achievement.rarity) }}
                  >
                    {achievement.name}
                  </div>
                  <div className="achievement-rarity">{getRarityDisplayName(achievement.rarity)}</div>
                </div>
                {achievement.completed && (
                  <div className="completion-badge">✓</div>
                )}
              </div>

              <div className="achievement-description">
                {achievement.description}
              </div>

              <div className="achievement-progress">
                <div className="progress-text">
                  进度: {formatProgress(achievement.current, achievement.target, achievement.type)}
                </div>
                <div className="progress-bar">
                  <div 
                    className="progress-fill"
                    style={{ 
                      width: `${Math.min((achievement.current / achievement.target) * 100, 100)}%`,
                      backgroundColor: achievement.completed ? '#4CAF50' : '#FFD700'
                    }}
                  />
                </div>
              </div>

              <div className="achievement-rewards">
                <div className="rewards-label">奖励:</div>
                <div className="rewards-list">
                  {achievement.reward.gold && (
                    <span className="reward-item">💰 {achievement.reward.gold}</span>
                  )}
                  {achievement.reward.experience && (
                    <span className="reward-item">⭐ {achievement.reward.experience} 经验</span>
                  )}
                  {achievement.reward.item && (
                    <span className="reward-item">🎁 {achievement.reward.item}</span>
                  )}
                </div>
              </div>

              {achievement.completed && !(achievement as any).claimed && (
                <button 
                  className="claim-button"
                  onClick={() => handleClaimReward(achievement)}
                >
                  领取奖励
                </button>
              )}

              {(achievement as any).claimed && (
                <div className="claimed-badge">已领取</div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

const getRarityDisplayName = (rarity: string): string => {
  const names = {
    common: '普通',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说'
  };
  return names[rarity as keyof typeof names] || rarity;
};

export default AchievementPanel;
