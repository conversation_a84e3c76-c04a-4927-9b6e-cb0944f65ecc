import React, { useState, useEffect } from 'react';
import { useGameStore } from '../stores/gameStore';
import { generateRandomItem, calculateDamage, generateId } from '../utils/gameUtils';
import { playSound, SoundType } from '../utils/audioUtils';
import { Enemy } from '../types';
import './BattlePanel.css';

const BattlePanel: React.FC = () => {
  const {
    characters,
    activeParty,
    currentArea,
    areas,
    isAutoPlaying,
    addExperience,
    addToInventory,
    addGold,
    incrementKillCount
  } = useGameStore();

  const [currentEnemy, setCurrentEnemy] = useState<Enemy | null>(null);
  const [battleLog, setBattleLog] = useState<string[]>([]);
  const [isInBattle, setIsInBattle] = useState(false);
  const [battleProgress, setBattleProgress] = useState(0);

  const currentAreaData = areas.find(area => area.id === currentArea);
  const partyMembers = characters.filter(c => activeParty.includes(c.id));

  // 生成新敌人
  const generateNewEnemy = () => {
    if (currentAreaData && currentAreaData.enemies.length > 0) {
      const randomIndex = Math.floor(Math.random() * currentAreaData.enemies.length);
      const enemyTemplate = currentAreaData.enemies[randomIndex];
      const enemy = {
        ...enemyTemplate,
        id: generateId(),
        health: enemyTemplate.maxHealth
      };
      setCurrentEnemy(enemy);
      addToBattleLog(`遭遇了 ${enemy.name} (等级 ${enemy.level})`);
      playSound(SoundType.BATTLE_START);
    }
  };

  // 添加战斗日志
  const addToBattleLog = (message: string) => {
    setBattleLog(prev => {
      const newLog = [...prev, `[${new Date().toLocaleTimeString()}] ${message}`];
      return newLog.slice(-10); // 只保留最近10条记录
    });
  };

  // 执行战斗回合
  const executeBattleRound = () => {
    if (!currentEnemy || partyMembers.length === 0) return;

    let totalPartyDamage = 0;
    let totalEnemyDamage = 0;

    // 计算队伍总伤害
    partyMembers.forEach(character => {
      const damage = calculateDamage(character.stats, {
        strength: 0,
        dexterity: 0,
        intelligence: 0,
        vitality: 0,
        damage: 0,
        defense: currentEnemy.defense,
        health: 0,
        mana: 0
      });
      totalPartyDamage += damage;
    });

    // 计算敌人对队伍的伤害
    if (partyMembers.length > 0) {
      const targetCharacter = partyMembers[Math.floor(Math.random() * partyMembers.length)];
      totalEnemyDamage = calculateDamage(
        {
          strength: 0,
          dexterity: 0,
          intelligence: 0,
          vitality: 0,
          damage: currentEnemy.damage,
          defense: 0,
          health: 0,
          mana: 0
        },
        targetCharacter.stats
      );
    }

    // 应用伤害
    const newEnemyHealth = Math.max(0, currentEnemy.health - totalPartyDamage);
    setCurrentEnemy(prev => prev ? { ...prev, health: newEnemyHealth } : null);

    addToBattleLog(`队伍对 ${currentEnemy.name} 造成了 ${totalPartyDamage} 点伤害`);
    playSound(SoundType.BATTLE_HIT);

    if (newEnemyHealth <= 0) {
      // 敌人死亡
      handleEnemyDefeated();
    } else if (totalEnemyDamage > 0) {
      addToBattleLog(`${currentEnemy.name} 对队伍造成了 ${totalEnemyDamage} 点伤害`);
    }
  };

  // 处理敌人被击败
  const handleEnemyDefeated = () => {
    if (!currentEnemy) return;

    const expPerMember = Math.floor(currentEnemy.experience / partyMembers.length);
    
    // 给队伍成员分配经验
    partyMembers.forEach(character => {
      addExperience(character.id, expPerMember);
    });

    addToBattleLog(`击败了 ${currentEnemy.name}！获得 ${currentEnemy.experience} 经验值`);
    playSound(SoundType.BATTLE_WIN);
    incrementKillCount();

    // 掉落物品和金币
    currentEnemy.lootTable.forEach(loot => {
      if (Math.random() < loot.chance) {
        if (loot.itemId === 'gold') {
          const goldAmount = loot.quantity || 10;
          addGold(goldAmount);
          addToBattleLog(`获得了 ${goldAmount} 金币`);
          playSound(SoundType.GOLD_PICKUP);
        } else if (loot.itemId === 'random') {
          const item = generateRandomItem(currentEnemy.level);
          addToInventory(item);
          addToBattleLog(`获得了 ${item.name}`);
          playSound(SoundType.ITEM_PICKUP);
        }
      }
    });

    setIsInBattle(false);
    setBattleProgress(0);
    
    // 延迟生成新敌人
    setTimeout(() => {
      if (isAutoPlaying) {
        generateNewEnemy();
      }
    }, 2000);
  };

  // 开始战斗
  const startBattle = () => {
    if (partyMembers.length === 0) {
      addToBattleLog('需要至少一个队伍成员才能战斗！');
      return;
    }

    if (!currentEnemy) {
      generateNewEnemy();
    }

    setIsInBattle(true);
    setBattleProgress(0);
  };

  // 自动战斗逻辑
  useEffect(() => {
    let battleInterval: number;

    if (isInBattle && currentEnemy && currentEnemy.health > 0) {
      battleInterval = setInterval(() => {
        setBattleProgress(prev => {
          const newProgress = prev + 10;
          if (newProgress >= 100) {
            executeBattleRound();
            return 0;
          }
          return newProgress;
        });
      }, 100);
    }

    return () => {
      if (battleInterval) {
        clearInterval(battleInterval as any);
      }
    };
  }, [isInBattle, currentEnemy]);

  // 自动挂机逻辑
  useEffect(() => {
    if (isAutoPlaying && !isInBattle && partyMembers.length > 0) {
      const autoStartTimer = setTimeout(() => {
        startBattle();
      }, 1000);

      return () => clearTimeout(autoStartTimer);
    }
  }, [isAutoPlaying, isInBattle, partyMembers.length]);

  return (
    <div className="battle-panel">
      <div className="panel-title">战斗区域</div>
      
      <div className="area-info">
        <h3>{currentAreaData?.name || '未知区域'}</h3>
        <p>{currentAreaData?.description}</p>
        <div className="area-level">推荐等级: {currentAreaData?.level}</div>
      </div>

      <div className="battle-area">
        {currentEnemy ? (
          <div className="enemy-display">
            <div className="enemy-info">
              <h4>{currentEnemy.name}</h4>
              <div className="enemy-level">等级 {currentEnemy.level}</div>
            </div>
            
            <div className="enemy-health">
              <div className="health-bar">
                <div 
                  className="health-fill"
                  style={{ 
                    width: `${(currentEnemy.health / currentEnemy.maxHealth) * 100}%` 
                  }}
                />
              </div>
              <div className="health-text">
                {currentEnemy.health} / {currentEnemy.maxHealth}
              </div>
            </div>

            <div className="enemy-stats">
              <span>攻击: {currentEnemy.damage}</span>
              <span>防御: {currentEnemy.defense}</span>
            </div>

            {isInBattle && (
              <div className="battle-progress">
                <div className="progress-bar">
                  <div 
                    className="progress-fill"
                    style={{ width: `${battleProgress}%` }}
                  />
                </div>
                <div className="progress-text">战斗中...</div>
              </div>
            )}
          </div>
        ) : (
          <div className="no-enemy">
            <p>区域安全</p>
            <p>点击"开始战斗"寻找敌人</p>
          </div>
        )}
      </div>

      <div className="battle-controls">
        <button 
          className="game-button primary"
          onClick={startBattle}
          disabled={isInBattle || partyMembers.length === 0}
        >
          {isInBattle ? '战斗中...' : '开始战斗'}
        </button>
        
        <button 
          className="game-button"
          onClick={generateNewEnemy}
          disabled={isInBattle}
        >
          寻找新敌人
        </button>
      </div>

      <div className="party-status">
        <h4>队伍状态</h4>
        {partyMembers.length === 0 ? (
          <div className="no-party">没有队伍成员</div>
        ) : (
          <div className="party-members-status">
            {partyMembers.map(character => (
              <div key={character.id} className="member-status">
                <span className="member-name">{character.name}</span>
                <span className="member-health">
                  生命: {character.stats.health}
                </span>
                <span className="member-damage">
                  攻击: {character.stats.damage}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="battle-log">
        <h4>战斗日志</h4>
        <div className="log-content">
          {battleLog.length === 0 ? (
            <div className="no-log">暂无战斗记录</div>
          ) : (
            battleLog.map((log, index) => (
              <div key={index} className="log-entry">
                {log}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default BattlePanel;
