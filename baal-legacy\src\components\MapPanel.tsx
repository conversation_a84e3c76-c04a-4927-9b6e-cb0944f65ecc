import React from 'react';
import { useGameStore } from '../stores/gameStore';
import { mapDatabase, checkMapUnlock, getRecommendedArea } from '../utils/mapUtils';
import './MapPanel.css';

const MapPanel: React.FC = () => {
  const { 
    characters, 
    activeParty, 
    currentArea, 
    areas, 
    setCurrentArea 
  } = useGameStore();

  const partyMembers = characters.filter(c => activeParty.includes(c.id));
  const averageLevel = partyMembers.length > 0 
    ? Math.floor(partyMembers.reduce((sum, char) => sum + char.level, 0) / partyMembers.length)
    : 1;

  const currentAreaData = areas.find(area => area.id === currentArea);
  const recommendedArea = getRecommendedArea(averageLevel);

  const handleAreaSelect = (areaId: string) => {
    const area = areas.find(a => a.id === areaId);
    if (area && area.unlocked) {
      setCurrentArea(areaId);
    }
  };

  const getDifficultyColor = (areaLevel: number): string => {
    const levelDiff = areaLevel - averageLevel;
    if (levelDiff <= -3) return '#4CAF50'; // 简单 - 绿色
    if (levelDiff <= -1) return '#8BC34A'; // 容易 - 浅绿
    if (levelDiff <= 1) return '#FFD700';  // 适中 - 金色
    if (levelDiff <= 3) return '#FF9800';  // 困难 - 橙色
    return '#f44336'; // 极难 - 红色
  };

  const getDifficultyText = (areaLevel: number): string => {
    const levelDiff = areaLevel - averageLevel;
    if (levelDiff <= -3) return '简单';
    if (levelDiff <= -1) return '容易';
    if (levelDiff <= 1) return '适中';
    if (levelDiff <= 3) return '困难';
    return '极难';
  };

  return (
    <div className="map-panel">
      <div className="panel-title">世界地图</div>
      
      <div className="current-area-info">
        <h3>当前区域</h3>
        {currentAreaData && (
          <div className="area-card current">
            <div className="area-header">
              <span className="area-name">{currentAreaData.name}</span>
              <span 
                className="area-difficulty"
                style={{ color: getDifficultyColor(currentAreaData.level) }}
              >
                {getDifficultyText(currentAreaData.level)}
              </span>
            </div>
            <div className="area-level">等级 {currentAreaData.level}</div>
            <div className="area-description">{currentAreaData.description}</div>
            <div className="area-enemies">
              <span>敌人数量: {currentAreaData.enemies.length}</span>
            </div>
          </div>
        )}
      </div>

      {recommendedArea && recommendedArea.id !== currentArea && (
        <div className="recommended-area">
          <h4>🎯 推荐区域</h4>
          <div className="area-card recommended">
            <div className="area-header">
              <span className="area-name">{recommendedArea.name}</span>
              <span 
                className="area-difficulty"
                style={{ color: getDifficultyColor(recommendedArea.level) }}
              >
                {getDifficultyText(recommendedArea.level)}
              </span>
            </div>
            <div className="area-level">等级 {recommendedArea.level}</div>
            <div className="area-description">{recommendedArea.description}</div>
            <button 
              className="game-button primary"
              onClick={() => handleAreaSelect(recommendedArea.id)}
            >
              前往此区域
            </button>
          </div>
        </div>
      )}

      <div className="available-areas">
        <h3>可用区域</h3>
        <div className="areas-grid">
          {mapDatabase.map(area => {
            const areaData = areas.find(a => a.id === area.id);
            const isUnlocked = areaData?.unlocked || checkMapUnlock(area.id, averageLevel);
            const isCurrent = area.id === currentArea;
            
            return (
              <div 
                key={area.id}
                className={`area-card ${isCurrent ? 'current' : ''} ${isUnlocked ? 'unlocked' : 'locked'}`}
                onClick={() => isUnlocked && handleAreaSelect(area.id)}
              >
                <div className="area-header">
                  <span className="area-name">{area.name}</span>
                  {isUnlocked && (
                    <span 
                      className="area-difficulty"
                      style={{ color: getDifficultyColor(area.level) }}
                    >
                      {getDifficultyText(area.level)}
                    </span>
                  )}
                </div>
                
                <div className="area-level">
                  等级 {area.level}
                  {!isUnlocked && (
                    <span className="unlock-requirement">
                      (需要等级 {area.level - 2})
                    </span>
                  )}
                </div>
                
                <div className="area-description">{area.description}</div>
                
                {isUnlocked && areaData && (
                  <div className="area-stats">
                    <div className="area-enemies">
                      敌人: {areaData.enemies.length}种
                    </div>
                    <div className="area-rewards">
                      经验: {area.level * 15}+ | 金币: {area.level * 8}+
                    </div>
                  </div>
                )}
                
                {isCurrent && (
                  <div className="current-indicator">
                    📍 当前位置
                  </div>
                )}
                
                {!isUnlocked && (
                  <div className="locked-overlay">
                    🔒 未解锁
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      <div className="party-info">
        <h4>队伍信息</h4>
        <div className="party-stats">
          <div className="stat-item">
            <span>队伍人数:</span>
            <span>{partyMembers.length}/3</span>
          </div>
          <div className="stat-item">
            <span>平均等级:</span>
            <span>{averageLevel}</span>
          </div>
          <div className="stat-item">
            <span>总攻击力:</span>
            <span>{partyMembers.reduce((sum, char) => sum + char.stats.damage, 0)}</span>
          </div>
          <div className="stat-item">
            <span>总防御力:</span>
            <span>{partyMembers.reduce((sum, char) => sum + char.stats.defense, 0)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapPanel;
