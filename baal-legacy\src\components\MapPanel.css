.map-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.current-area-info {
  background: rgba(30, 30, 30, 0.6);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #444;
}

.current-area-info h3 {
  color: #FFD700;
  margin: 0 0 15px 0;
  font-size: 16px;
  border-bottom: 1px solid #8B4513;
  padding-bottom: 8px;
}

.recommended-area {
  background: rgba(76, 175, 80, 0.1);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #4CAF50;
}

.recommended-area h4 {
  color: #4CAF50;
  margin: 0 0 15px 0;
  font-size: 14px;
}

.available-areas h3 {
  color: #FFD700;
  margin: 0 0 15px 0;
  font-size: 16px;
  border-bottom: 1px solid #8B4513;
  padding-bottom: 8px;
}

.areas-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
}

.area-card {
  background: rgba(20, 20, 20, 0.8);
  border: 2px solid #444;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.area-card.unlocked:hover {
  border-color: #8B4513;
  box-shadow: 0 0 15px rgba(139, 69, 19, 0.3);
  transform: translateY(-2px);
}

.area-card.current {
  border-color: #FFD700;
  background: rgba(255, 215, 0, 0.1);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.area-card.recommended {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.area-card.locked {
  opacity: 0.6;
  cursor: not-allowed;
  border-color: #666;
}

.area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.area-name {
  font-weight: bold;
  color: #e0e0e0;
  font-size: 16px;
}

.area-difficulty {
  font-size: 12px;
  font-weight: bold;
  padding: 2px 8px;
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.3);
}

.area-level {
  color: #CD853F;
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: bold;
}

.unlock-requirement {
  color: #888;
  font-size: 11px;
  font-weight: normal;
  margin-left: 8px;
}

.area-description {
  color: #ccc;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 12px;
}

.area-stats {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 10px;
}

.area-enemies,
.area-rewards {
  color: #87CEEB;
  font-size: 11px;
}

.current-indicator {
  background: #FFD700;
  color: #000;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
  text-align: center;
  margin-top: 10px;
}

.locked-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: #888;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
}

.party-info {
  background: rgba(30, 30, 30, 0.6);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #444;
  margin-top: auto;
}

.party-info h4 {
  color: #FFD700;
  margin: 0 0 15px 0;
  font-size: 14px;
  border-bottom: 1px solid #8B4513;
  padding-bottom: 8px;
}

.party-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: rgba(20, 20, 20, 0.5);
  border-radius: 4px;
  font-size: 12px;
}

.stat-item span:first-child {
  color: #ccc;
}

.stat-item span:last-child {
  color: #e0e0e0;
  font-weight: bold;
}

/* 滚动条样式 */
.map-panel::-webkit-scrollbar {
  width: 8px;
}

.map-panel::-webkit-scrollbar-track {
  background: rgba(20, 20, 20, 0.5);
  border-radius: 4px;
}

.map-panel::-webkit-scrollbar-thumb {
  background: #8B4513;
  border-radius: 4px;
}

.map-panel::-webkit-scrollbar-thumb:hover {
  background: #A0522D;
}

@media (max-width: 900px) {
  .areas-grid {
    grid-template-columns: 1fr;
  }
  
  .party-stats {
    grid-template-columns: 1fr;
  }
  
  .area-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
