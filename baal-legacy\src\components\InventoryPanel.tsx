import React, { useState } from 'react';
import { useGameStore } from '../stores/gameStore';
import { Item, ItemRarity } from '../types';
import ItemCard from './ItemCard';
import './InventoryPanel.css';

const InventoryPanel: React.FC = () => {
  const { inventory, removeFromInventory, gold, addGold } = useGameStore();
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [filterRarity, setFilterRarity] = useState<ItemRarity | 'all'>('all');

  const filteredInventory = inventory.filter(item => 
    filterRarity === 'all' || item.rarity === filterRarity
  );

  const handleSellItem = (item: Item) => {
    const sellPrice = calculateSellPrice(item);
    removeFromInventory(item.id);
    addGold(sellPrice);
    if (selectedItem?.id === item.id) {
      setSelectedItem(null);
    }
  };

  const calculateSellPrice = (item: Item): number => {
    const basePrice = item.level * 5;
    const rarityMultiplier = {
      [ItemRarity.NORMAL]: 1,
      [ItemRarity.MAGIC]: 2,
      [ItemRarity.RARE]: 5,
      [ItemRarity.UNIQUE]: 10,
      [ItemRarity.LEGENDARY]: 20
    };
    return Math.floor(basePrice * rarityMultiplier[item.rarity]);
  };

  const getRarityDisplayName = (rarity: ItemRarity): string => {
    const names = {
      [ItemRarity.NORMAL]: '普通',
      [ItemRarity.MAGIC]: '魔法',
      [ItemRarity.RARE]: '稀有',
      [ItemRarity.UNIQUE]: '独特',
      [ItemRarity.LEGENDARY]: '传说'
    };
    return names[rarity];
  };

  const getRarityColor = (rarity: ItemRarity): string => {
    const colors = {
      [ItemRarity.NORMAL]: '#ffffff',
      [ItemRarity.MAGIC]: '#4169E1',
      [ItemRarity.RARE]: '#FFD700',
      [ItemRarity.UNIQUE]: '#8B4513',
      [ItemRarity.LEGENDARY]: '#FF4500'
    };
    return colors[rarity];
  };

  return (
    <div className="inventory-panel">
      <div className="panel-title">背包</div>
      
      <div className="inventory-header">
        <div className="inventory-stats">
          <span>物品: {inventory.length}/100</span>
          <span className="gold-display">💰 {gold}</span>
        </div>
        
        <div className="rarity-filter">
          <select 
            value={filterRarity} 
            onChange={(e) => setFilterRarity(e.target.value as ItemRarity | 'all')}
            className="filter-select"
          >
            <option value="all">全部</option>
            {Object.values(ItemRarity).map(rarity => (
              <option key={rarity} value={rarity}>
                {getRarityDisplayName(rarity)}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="inventory-content">
        <div className="items-grid">
          {filteredInventory.length === 0 ? (
            <div className="empty-inventory">
              {inventory.length === 0 ? '背包为空' : '没有符合条件的物品'}
            </div>
          ) : (
            filteredInventory.map(item => (
              <div
                key={item.id}
                className={`item-slot ${selectedItem?.id === item.id ? 'selected' : ''}`}
                onClick={() => setSelectedItem(item)}
              >
                <div 
                  className="item-icon"
                  style={{ borderColor: getRarityColor(item.rarity) }}
                >
                  {getItemIcon(item)}
                </div>
                <div className="item-name" style={{ color: getRarityColor(item.rarity) }}>
                  {item.name}
                </div>
                <div className="item-level">Lv.{item.level}</div>
              </div>
            ))
          )}
        </div>

        {selectedItem && (
          <div className="item-details">
            <ItemCard 
              item={selectedItem} 
              onSell={() => handleSellItem(selectedItem)}
              sellPrice={calculateSellPrice(selectedItem)}
            />
          </div>
        )}
      </div>

      <div className="inventory-actions">
        <button 
          className="game-button"
          onClick={() => {
            // 出售所有普通物品
            const normalItems = inventory.filter(item => item.rarity === ItemRarity.NORMAL);
            let totalGold = 0;
            normalItems.forEach(item => {
              totalGold += calculateSellPrice(item);
              removeFromInventory(item.id);
            });
            if (totalGold > 0) {
              addGold(totalGold);
            }
            if (selectedItem && normalItems.some(item => item.id === selectedItem.id)) {
              setSelectedItem(null);
            }
          }}
          disabled={!inventory.some(item => item.rarity === ItemRarity.NORMAL)}
        >
          出售普通物品
        </button>
        
        <button 
          className="game-button"
          onClick={() => {
            // 清空背包（确认对话框在实际应用中应该添加）
            inventory.forEach(item => removeFromInventory(item.id));
            setSelectedItem(null);
          }}
          disabled={inventory.length === 0}
        >
          清空背包
        </button>
      </div>
    </div>
  );
};

const getItemIcon = (item: Item): string => {
  const icons = {
    weapon: '⚔️',
    armor: '🛡️',
    helmet: '⛑️',
    boots: '👢',
    gloves: '🧤',
    ring: '💍',
    amulet: '📿'
  };
  return icons[item.type] || '📦';
};

export default InventoryPanel;
