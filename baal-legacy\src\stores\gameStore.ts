import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { GameState, CharacterClass, Item } from '../types';
import { generateCharacter } from '../utils/gameUtils';
import { initializeMaps } from '../utils/mapUtils';

interface GameStore extends GameState {
  // Actions
  addCharacter: (characterClass: CharacterClass, name: string) => void;
  removeCharacter: (characterId: string) => void;
  setActiveParty: (characterIds: string[]) => void;
  addExperience: (characterId: string, amount: number) => void;
  equipItem: (characterId: string, item: Item) => void;
  addToInventory: (item: Item) => void;
  removeFromInventory: (itemId: string) => void;
  setCurrentArea: (areaId: string) => void;
  addGold: (amount: number) => void;
  spendGold: (amount: number) => boolean;
  toggleAutoPlay: () => void;
  updateGameTime: (deltaTime: number) => void;
  incrementKillCount: () => void;
  incrementGoldEarned: (amount: number) => void;
  saveGame: () => void;
  loadGame: () => void;
}

const initialState: GameState = {
  characters: [],
  activeParty: [],
  currentArea: 'graveyard',
  areas: initializeMaps(),
  inventory: [],
  gold: 100,
  isAutoPlaying: false,
  gameTime: 0,
  totalKills: 0,
  totalGoldEarned: 0,
  settings: {
    autoSell: false,
    autoEquip: true,
    soundEnabled: true,
    musicEnabled: true,
    gameSpeed: 1.0
  }
};

export const useGameStore = create<GameStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      addCharacter: (characterClass: CharacterClass, name: string) => {
        const newCharacter = generateCharacter(characterClass, name);
        set((state) => ({
          characters: [...state.characters, newCharacter]
        }));
      },

      removeCharacter: (characterId: string) => {
        set((state) => ({
          characters: state.characters.filter(c => c.id !== characterId),
          activeParty: state.activeParty.filter(id => id !== characterId)
        }));
      },

      setActiveParty: (characterIds: string[]) => {
        // 最多3个角色
        const limitedParty = characterIds.slice(0, 3);
        set({ activeParty: limitedParty });
      },

      addExperience: (characterId: string, amount: number) => {
        set((state) => ({
          characters: state.characters.map(character => {
            if (character.id === characterId) {
              const newExp = character.experience + amount;
              let newLevel = character.level;
              let expToNext = character.experienceToNext;

              // 检查是否升级
              while (newExp >= expToNext && newLevel < 100) {
                newLevel++;
                expToNext = newLevel * 100; // 简单的升级公式
              }

              return {
                ...character,
                experience: newExp,
                level: newLevel,
                experienceToNext: expToNext
              };
            }
            return character;
          })
        }));
      },

      equipItem: (characterId: string, item: Item) => {
        set((state) => ({
          characters: state.characters.map(character => {
            if (character.id === characterId) {
              const newEquipment = { ...character.equipment };
              newEquipment[item.type] = item;
              return {
                ...character,
                equipment: newEquipment
              };
            }
            return character;
          })
        }));
      },

      addToInventory: (item: Item) => {
        set((state) => ({
          inventory: [...state.inventory, item]
        }));
      },

      removeFromInventory: (itemId: string) => {
        set((state) => ({
          inventory: state.inventory.filter(item => item.id !== itemId)
        }));
      },

      setCurrentArea: (areaId: string) => {
        set({ currentArea: areaId });
      },

      addGold: (amount: number) => {
        set((state) => ({
          gold: state.gold + amount,
          totalGoldEarned: state.totalGoldEarned + amount
        }));
      },

      spendGold: (amount: number) => {
        const state = get();
        if (state.gold >= amount) {
          set({ gold: state.gold - amount });
          return true;
        }
        return false;
      },

      toggleAutoPlay: () => {
        set((state) => ({
          isAutoPlaying: !state.isAutoPlaying
        }));
      },

      updateGameTime: (deltaTime: number) => {
        set((state) => ({
          gameTime: state.gameTime + deltaTime
        }));
      },

      incrementKillCount: () => {
        set((state) => ({
          totalKills: state.totalKills + 1
        }));
      },

      incrementGoldEarned: (amount: number) => {
        set((state) => ({
          totalGoldEarned: state.totalGoldEarned + amount
        }));
      },

      saveGame: () => {
        // 由于使用了persist中间件，数据会自动保存
        console.log('游戏已保存');
      },

      loadGame: () => {
        // 由于使用了persist中间件，数据会自动加载
        console.log('游戏已加载');
      }
    }),
    {
      name: 'baal-legacy-save',
      version: 1,
      storage: createJSONStorage(() => localStorage)
    }
  )
);
