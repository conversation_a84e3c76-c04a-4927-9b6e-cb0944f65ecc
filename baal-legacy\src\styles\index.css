/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d1810 100%);
  color: #e0e0e0;
  overflow: hidden;
}

#root {
  height: 100vh;
  width: 100vw;
}

/* 暗黑风格的滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* 游戏UI基础样式 */
.game-panel {
  background: rgba(20, 20, 20, 0.9);
  border: 2px solid #8B4513;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(139, 69, 19, 0.3);
}

.game-button {
  background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
  border: 1px solid #8B4513;
  color: #e0e0e0;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.game-button:hover {
  background: linear-gradient(145deg, #4a4a4a, #3a3a3a);
  box-shadow: 0 0 10px rgba(139, 69, 19, 0.5);
}

.game-button:active {
  transform: translateY(1px);
}

/* 装备品质颜色 */
.item-normal { color: #ffffff; }
.item-magic { color: #4169E1; }
.item-rare { color: #FFD700; }
.item-unique { color: #8B4513; }
.item-legendary { color: #FF4500; }

/* 动画效果 */
@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(139, 69, 19, 0.5); }
  50% { box-shadow: 0 0 20px rgba(139, 69, 19, 0.8); }
}

.glow-animation {
  animation: glow 2s ease-in-out infinite;
}
