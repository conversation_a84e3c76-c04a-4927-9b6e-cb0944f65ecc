#!/usr/bin/env python3
import http.server
import socketserver
import webbrowser
import os
import sys

PORT = 3000

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

    def do_GET(self):
        if self.path == '/':
            self.path = '/demo.html'
        return super().do_GET()

def start_server():
    try:
        with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
            print(f"🎮 巴尔的遗产游戏服务器启动成功！")
            print(f"🌐 访问地址: http://localhost:{PORT}")
            print(f"📱 演示页面: http://localhost:{PORT}/demo.html")
            print(f"⚔️ 开始你的冒险之旅！")
            print(f"💡 按 Ctrl+C 停止服务器")
            print("-" * 50)
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{PORT}')
                print("✅ 浏览器已自动打开")
            except:
                print("⚠️ 请手动打开浏览器访问上述地址")
            
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        sys.exit(0)
    except OSError as e:
        if e.errno == 98:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用，尝试使用端口 {PORT + 1}")
            PORT = PORT + 1
            start_server()
        else:
            print(f"❌ 启动服务器失败: {e}")
            sys.exit(1)

if __name__ == "__main__":
    # 切换到脚本所在目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    start_server()
