import React from 'react';
import { Item, ItemRarity, StatType } from '../types';
import './ItemCard.css';

interface ItemCardProps {
  item: Item;
  onSell?: () => void;
  onEquip?: () => void;
  sellPrice?: number;
  showActions?: boolean;
}

const ItemCard: React.FC<ItemCardProps> = ({
  item,
  onSell,
  onEquip,
  sellPrice,
  showActions = true
}) => {
  const getRarityDisplayName = (rarity: ItemRarity): string => {
    const names = {
      [ItemRarity.NORMAL]: '普通',
      [ItemRarity.MAGIC]: '魔法',
      [ItemRarity.RARE]: '稀有',
      [ItemRarity.UNIQUE]: '独特',
      [ItemRarity.LEGENDARY]: '传说'
    };
    return names[rarity];
  };

  const getRarityColor = (rarity: ItemRarity): string => {
    const colors = {
      [ItemRarity.NORMAL]: '#ffffff',
      [ItemRarity.MAGIC]: '#4169E1',
      [ItemRarity.RARE]: '#FFD700',
      [ItemRarity.UNIQUE]: '#8B4513',
      [ItemRarity.LEGENDARY]: '#FF4500'
    };
    return colors[rarity];
  };

  const getStatDisplayName = (statType: StatType): string => {
    const names = {
      [StatType.STRENGTH]: '力量',
      [StatType.DEXTERITY]: '敏捷',
      [StatType.INTELLIGENCE]: '智力',
      [StatType.VITALITY]: '体力',
      [StatType.DAMAGE]: '攻击力',
      [StatType.DEFENSE]: '防御力',
      [StatType.HEALTH]: '生命值',
      [StatType.MANA]: '法力值'
    };
    return names[statType];
  };

  const getTypeDisplayName = (type: string): string => {
    const names: Record<string, string> = {
      weapon: '武器',
      armor: '护甲',
      helmet: '头盔',
      boots: '靴子',
      gloves: '手套',
      ring: '戒指',
      amulet: '项链'
    };
    return names[type] || type;
  };

  const getItemIcon = (): string => {
    const icons = {
      weapon: '⚔️',
      armor: '🛡️',
      helmet: '⛑️',
      boots: '👢',
      gloves: '🧤',
      ring: '💍',
      amulet: '📿'
    };
    return icons[item.type] || '📦';
  };

  return (
    <div className="item-card" style={{ borderColor: getRarityColor(item.rarity) }}>
      <div className="item-header">
        <div className="item-icon-large">
          {getItemIcon()}
        </div>
        <div className="item-basic-info">
          <div 
            className="item-name-large" 
            style={{ color: getRarityColor(item.rarity) }}
          >
            {item.name}
          </div>
          <div className="item-type">{getTypeDisplayName(item.type)}</div>
          <div className="item-rarity-level">
            <span 
              className="item-rarity"
              style={{ color: getRarityColor(item.rarity) }}
            >
              {getRarityDisplayName(item.rarity)}
            </span>
            <span className="item-level">等级 {item.level}</span>
          </div>
        </div>
      </div>

      {item.description && (
        <div className="item-description">
          {item.description}
        </div>
      )}

      {item.stats.length > 0 && (
        <div className="item-stats">
          <h5>属性:</h5>
          <div className="stats-list">
            {item.stats.map((stat, index) => (
              <div key={index} className="stat-line">
                <span className="stat-name">{getStatDisplayName(stat.type)}:</span>
                <span className="stat-value">
                  +{stat.value}{stat.isPercentage ? '%' : ''}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {item.sockets && item.sockets > 0 && (
        <div className="item-sockets">
          <h5>镶嵌孔:</h5>
          <div className="sockets-display">
            {Array.from({ length: item.sockets }, (_, index) => (
              <div key={index} className="socket">
                {item.socketedRunes && item.socketedRunes[index] ? (
                  <span className="socketed-rune">
                    {item.socketedRunes[index].name}
                  </span>
                ) : (
                  <span className="empty-socket">○</span>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {showActions && (
        <div className="item-actions">
          {onEquip && (
            <button className="game-button primary" onClick={onEquip}>
              装备
            </button>
          )}
          {onSell && sellPrice !== undefined && (
            <button className="game-button" onClick={onSell}>
              出售 ({sellPrice}💰)
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default ItemCard;
