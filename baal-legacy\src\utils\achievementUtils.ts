import { generateId } from './gameUtils';

// 成就类型
export enum AchievementType {
  LEVEL = 'level',
  KILL = 'kill',
  EQUIPMENT = 'equipment',
  GOLD = 'gold',
  AREA = 'area',
  SKILL = 'skill',
  TIME = 'time'
}

// 成就接口
export interface Achievement {
  id: string;
  name: string;
  description: string;
  type: AchievementType;
  target: number;
  current: number;
  completed: boolean;
  reward: {
    gold?: number;
    experience?: number;
    item?: string;
  };
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

// 成就数据库
export const achievementDatabase: Achievement[] = [
  // 等级成就
  {
    id: generateId(),
    name: '初出茅庐',
    description: '任意角色达到5级',
    type: AchievementType.LEVEL,
    target: 5,
    current: 0,
    completed: false,
    reward: { gold: 100, experience: 50 },
    icon: '🌟',
    rarity: 'common'
  },
  {
    id: generateId(),
    name: '小有成就',
    description: '任意角色达到10级',
    type: AchievementType.LEVEL,
    target: 10,
    current: 0,
    completed: false,
    reward: { gold: 300, experience: 150 },
    icon: '⭐',
    rarity: 'common'
  },
  {
    id: generateId(),
    name: '实力强者',
    description: '任意角色达到20级',
    type: AchievementType.LEVEL,
    target: 20,
    current: 0,
    completed: false,
    reward: { gold: 1000, experience: 500 },
    icon: '💫',
    rarity: 'rare'
  },
  {
    id: generateId(),
    name: '传奇英雄',
    description: '任意角色达到50级',
    type: AchievementType.LEVEL,
    target: 50,
    current: 0,
    completed: false,
    reward: { gold: 5000, experience: 2000 },
    icon: '🏆',
    rarity: 'legendary'
  },

  // 击杀成就
  {
    id: generateId(),
    name: '新手猎人',
    description: '击败100个敌人',
    type: AchievementType.KILL,
    target: 100,
    current: 0,
    completed: false,
    reward: { gold: 200, experience: 100 },
    icon: '⚔️',
    rarity: 'common'
  },
  {
    id: generateId(),
    name: '怪物杀手',
    description: '击败500个敌人',
    type: AchievementType.KILL,
    target: 500,
    current: 0,
    completed: false,
    reward: { gold: 800, experience: 400 },
    icon: '🗡️',
    rarity: 'rare'
  },
  {
    id: generateId(),
    name: '屠戮者',
    description: '击败2000个敌人',
    type: AchievementType.KILL,
    target: 2000,
    current: 0,
    completed: false,
    reward: { gold: 3000, experience: 1500 },
    icon: '💀',
    rarity: 'epic'
  },

  // 装备成就
  {
    id: generateId(),
    name: '装备收集家',
    description: '获得10件稀有装备',
    type: AchievementType.EQUIPMENT,
    target: 10,
    current: 0,
    completed: false,
    reward: { gold: 500 },
    icon: '🎒',
    rarity: 'common'
  },
  {
    id: generateId(),
    name: '传说猎人',
    description: '获得5件传说装备',
    type: AchievementType.EQUIPMENT,
    target: 5,
    current: 0,
    completed: false,
    reward: { gold: 2000 },
    icon: '👑',
    rarity: 'epic'
  },

  // 金币成就
  {
    id: generateId(),
    name: '小富翁',
    description: '累计获得10,000金币',
    type: AchievementType.GOLD,
    target: 10000,
    current: 0,
    completed: false,
    reward: { experience: 500 },
    icon: '💰',
    rarity: 'common'
  },
  {
    id: generateId(),
    name: '大富豪',
    description: '累计获得100,000金币',
    type: AchievementType.GOLD,
    target: 100000,
    current: 0,
    completed: false,
    reward: { experience: 2000 },
    icon: '💎',
    rarity: 'rare'
  },

  // 区域成就
  {
    id: generateId(),
    name: '探索者',
    description: '解锁3个新区域',
    type: AchievementType.AREA,
    target: 3,
    current: 0,
    completed: false,
    reward: { gold: 800, experience: 300 },
    icon: '🗺️',
    rarity: 'common'
  },
  {
    id: generateId(),
    name: '世界旅行家',
    description: '解锁所有区域',
    type: AchievementType.AREA,
    target: 7,
    current: 0,
    completed: false,
    reward: { gold: 5000, experience: 2000 },
    icon: '🌍',
    rarity: 'legendary'
  },

  // 技能成就
  {
    id: generateId(),
    name: '技能学徒',
    description: '学会10个技能',
    type: AchievementType.SKILL,
    target: 10,
    current: 0,
    completed: false,
    reward: { gold: 600, experience: 200 },
    icon: '📚',
    rarity: 'common'
  },
  {
    id: generateId(),
    name: '技能大师',
    description: '学会所有职业的技能',
    type: AchievementType.SKILL,
    target: 20,
    current: 0,
    completed: false,
    reward: { gold: 3000, experience: 1000 },
    icon: '🧙‍♂️',
    rarity: 'epic'
  },

  // 时间成就
  {
    id: generateId(),
    name: '坚持不懈',
    description: '游戏时间达到1小时',
    type: AchievementType.TIME,
    target: 3600, // 秒
    current: 0,
    completed: false,
    reward: { gold: 300, experience: 100 },
    icon: '⏰',
    rarity: 'common'
  },
  {
    id: generateId(),
    name: '资深玩家',
    description: '游戏时间达到10小时',
    type: AchievementType.TIME,
    target: 36000,
    current: 0,
    completed: false,
    reward: { gold: 2000, experience: 800 },
    icon: '⏳',
    rarity: 'rare'
  }
];

// 检查成就进度
export const checkAchievementProgress = (
  achievement: Achievement,
  gameState: any
): Achievement => {
  let current = achievement.current;

  switch (achievement.type) {
    case AchievementType.LEVEL:
      current = Math.max(...gameState.characters.map((c: any) => c.level));
      break;
    case AchievementType.KILL:
      // 这里需要在游戏状态中添加击杀计数
      current = gameState.totalKills || 0;
      break;
    case AchievementType.EQUIPMENT:
      current = gameState.inventory.filter((item: any) => 
        item.rarity === 'rare' || item.rarity === 'unique' || item.rarity === 'legendary'
      ).length;
      break;
    case AchievementType.GOLD:
      current = gameState.totalGoldEarned || 0;
      break;
    case AchievementType.AREA:
      current = gameState.areas.filter((area: any) => area.unlocked).length;
      break;
    case AchievementType.SKILL:
      current = gameState.characters.reduce((total: number, char: any) => 
        total + char.skills.length, 0
      );
      break;
    case AchievementType.TIME:
      current = gameState.gameTime;
      break;
  }

  const completed = current >= achievement.target;

  return {
    ...achievement,
    current,
    completed
  };
};

// 获取成就奖励
export const claimAchievementReward = (achievement: Achievement) => {
  return {
    gold: achievement.reward.gold || 0,
    experience: achievement.reward.experience || 0,
    item: achievement.reward.item
  };
};

// 获取成就稀有度颜色
export const getAchievementRarityColor = (rarity: string): string => {
  const colors = {
    common: '#ffffff',
    rare: '#4169E1',
    epic: '#9932CC',
    legendary: '#FF4500'
  };
  return colors[rarity as keyof typeof colors] || '#ffffff';
};

// 格式化进度显示
export const formatProgress = (current: number, target: number, type: AchievementType): string => {
  switch (type) {
    case AchievementType.TIME:
      const currentHours = Math.floor(current / 3600);
      const currentMinutes = Math.floor((current % 3600) / 60);
      const targetHours = Math.floor(target / 3600);
      const targetMinutes = Math.floor((target % 3600) / 60);
      
      if (targetHours > 0) {
        return `${currentHours}:${currentMinutes.toString().padStart(2, '0')} / ${targetHours}:${targetMinutes.toString().padStart(2, '0')}`;
      }
      return `${currentMinutes}分钟 / ${targetMinutes}分钟`;
    
    case AchievementType.GOLD:
      return `${current.toLocaleString()} / ${target.toLocaleString()}`;
    
    default:
      return `${current} / ${target}`;
  }
};
