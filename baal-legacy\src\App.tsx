import React, { useEffect, useState } from 'react';
import { useGameStore } from './stores/gameStore';
import GameHeader from './components/GameHeader';
import CharacterPanel from './components/CharacterPanel';
import InventoryPanel from './components/InventoryPanel';
import BattlePanel from './components/BattlePanel';
import ControlPanel from './components/ControlPanel';
import SkillPanel from './components/SkillPanel';
import MapPanel from './components/MapPanel';
import AchievementPanel from './components/AchievementPanel';
import { initializeAudio, playSound, SoundType } from './utils/audioUtils';
import './styles/App.css';

const App: React.FC = () => {
  const {
    characters,
    activeParty,
    isAutoPlaying,
    updateGameTime,
    gameTime
  } = useGameStore();

  const [activeTab, setActiveTab] = useState<'battle' | 'skills' | 'map' | 'achievements'>('battle');

  // 初始化音频
  useEffect(() => {
    const handleUserInteraction = () => {
      initializeAudio();
      document.removeEventListener('click', handleUserInteraction);
      document.removeEventListener('keydown', handleUserInteraction);
    };

    document.addEventListener('click', handleUserInteraction);
    document.addEventListener('keydown', handleUserInteraction);

    return () => {
      document.removeEventListener('click', handleUserInteraction);
      document.removeEventListener('keydown', handleUserInteraction);
    };
  }, []);

  // 游戏主循环
  useEffect(() => {
    let animationFrame: number;
    let lastTime = Date.now();

    const gameLoop = () => {
      const currentTime = Date.now();
      const deltaTime = (currentTime - lastTime) / 1000; // 转换为秒
      lastTime = currentTime;

      // 更新游戏时间
      updateGameTime(deltaTime);

      // 如果开启自动游戏，执行自动逻辑
      if (isAutoPlaying && activeParty.length > 0) {
        // 这里会添加自动战斗逻辑
      }

      animationFrame = requestAnimationFrame(gameLoop);
    };

    gameLoop();

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isAutoPlaying, activeParty, updateGameTime]);

  // 标签页切换音效
  const handleTabChange = (tab: typeof activeTab) => {
    playSound(SoundType.CLICK);
    setActiveTab(tab);
  };

  return (
    <div className="app">
      <GameHeader />
      
      <div className="game-layout">
        {/* 左侧面板 - 角色信息 */}
        <div className="left-panel">
          <CharacterPanel />
        </div>

        {/* 中央面板 - 战斗区域/技能系统 */}
        <div className="center-panel">
          <div className="center-panel-tabs">
            <button
              className={`tab-button ${activeTab === 'battle' ? 'active' : ''}`}
              onClick={() => handleTabChange('battle')}
            >
              ⚔️ 战斗
            </button>
            <button
              className={`tab-button ${activeTab === 'skills' ? 'active' : ''}`}
              onClick={() => handleTabChange('skills')}
            >
              ✨ 技能
            </button>
            <button
              className={`tab-button ${activeTab === 'map' ? 'active' : ''}`}
              onClick={() => handleTabChange('map')}
            >
              🗺️ 地图
            </button>
            <button
              className={`tab-button ${activeTab === 'achievements' ? 'active' : ''}`}
              onClick={() => handleTabChange('achievements')}
            >
              🏆 成就
            </button>
          </div>

          <div className="center-panel-content">
            {activeTab === 'battle' && <BattlePanel />}
            {activeTab === 'skills' && <SkillPanel />}
            {activeTab === 'map' && <MapPanel />}
            {activeTab === 'achievements' && <AchievementPanel />}
          </div>
        </div>

        {/* 右侧面板 - 背包和控制 */}
        <div className="right-panel">
          <InventoryPanel />
          <ControlPanel />
        </div>
      </div>

      {/* 游戏状态显示 */}
      <div className="game-status">
        <div className="status-item">
          游戏时间: {Math.floor(gameTime / 60)}:{Math.floor(gameTime % 60).toString().padStart(2, '0')}
        </div>
        <div className="status-item">
          角色数量: {characters.length}
        </div>
        <div className="status-item">
          队伍: {activeParty.length}/3
        </div>
        <div className="status-item">
          状态: {isAutoPlaying ? '自动挂机中' : '手动模式'}
        </div>
      </div>
    </div>
  );
};

export default App;
