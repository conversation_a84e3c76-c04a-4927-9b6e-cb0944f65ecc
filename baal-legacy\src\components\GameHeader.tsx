import React from 'react';
import { useGameStore } from '../stores/gameStore';
import { formatNumber } from '../utils/gameUtils';
import './GameHeader.css';

const GameHeader: React.FC = () => {
  const { gold, currentArea, areas, saveGame, loadGame } = useGameStore();
  
  const currentAreaData = areas.find(area => area.id === currentArea);

  return (
    <header className="game-header">
      <div className="header-left">
        <h1 className="game-title">巴尔的遗产</h1>
        <div className="game-subtitle"><PERSON><PERSON>'s Legacy</div>
      </div>

      <div className="header-center">
        <div className="current-area">
          <span className="area-label">当前区域:</span>
          <span className="area-name">{currentAreaData?.name || '未知区域'}</span>
          <span className="area-level">Lv.{currentAreaData?.level || 1}</span>
        </div>
      </div>

      <div className="header-right">
        <div className="gold-display">
          <span className="gold-icon">💰</span>
          <span className="gold-amount">{formatNumber(gold)}</span>
        </div>
        
        <div className="header-buttons">
          <button className="game-button" onClick={saveGame}>
            保存游戏
          </button>
          <button className="game-button" onClick={loadGame}>
            加载游戏
          </button>
        </div>
      </div>
    </header>
  );
};

export default GameHeader;
