# 巴尔的遗产 (<PERSON><PERSON>'s Legacy)

一款致敬《暗黑破坏神2》的网页挂机RPG游戏

## 🎮 游戏特色

### 核心玩法
- **桌面侧栏挂机** - 类似原作的挂机版本，可以一边工作一边游戏
- **佣兵团队系统** - 最多3人小队，策略搭配不同职业
- **自动战斗** - 智能AI自动战斗、拾取、升级
- **丰富装备系统** - 1000+独特装备，随机属性生成

### 四大职业
1. **🧙‍♂️ 法师 (Mage)**
   - 高法术伤害和法力值
   - 擅长元素魔法和范围攻击
   - 脆弱但输出强大

2. **🛡️ 圣骑士 (Paladin)**
   - 平衡的攻防能力
   - 拥有治疗和光环技能
   - 团队支援型角色

3. **🏹 游侠 (Ranger)**
   - 高敏捷和攻击速度
   - 远程攻击和陷阱技能
   - 灵活的战术选择

4. **⚔️ 狂战士 (Barbarian)**
   - 最高的物理攻击和生命值
   - 强大的近战能力
   - 坦克型前排角色

### 装备系统
- **5个稀有度等级**: 普通、魔法、稀有、独特、传说
- **随机属性生成**: 每件装备都有独特的属性组合
- **镶嵌系统**: 符文镶嵌增强装备能力
- **装备升级**: 通过材料强化装备属性

### 技能系统
- **72种技能**: 每个职业18种独特技能
- **技能树**: 逐步解锁更强大的能力
- **光环效果**: 团队增益技能
- **主动/被动**: 多样化的技能类型

## 🛠️ 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **状态管理**: Zustand
- **样式方案**: CSS Modules + 原生CSS
- **动画效果**: Framer Motion
- **数据持久化**: LocalStorage + IndexedDB

## 📁 项目结构

```
baal-legacy/
├── src/
│   ├── components/          # React组件
│   │   ├── GameHeader.tsx   # 游戏头部
│   │   ├── CharacterPanel.tsx # 角色面板
│   │   ├── BattlePanel.tsx  # 战斗面板
│   │   ├── InventoryPanel.tsx # 背包面板
│   │   └── ControlPanel.tsx # 控制面板
│   ├── stores/              # 状态管理
│   │   └── gameStore.ts     # 游戏主状态
│   ├── types/               # TypeScript类型定义
│   │   └── index.ts         # 游戏类型
│   ├── utils/               # 工具函数
│   │   └── gameUtils.ts     # 游戏逻辑工具
│   └── styles/              # 样式文件
├── demo.html               # 演示页面
└── README.md              # 项目说明
```

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览演示
直接在浏览器中打开 `demo.html` 文件查看游戏界面演示。

## 🎯 游戏机制

### 战斗系统
- **实时战斗**: 基于属性的伤害计算
- **队伍协作**: 多角色协同作战
- **技能释放**: 主动技能和被动效果
- **经验获取**: 击败敌人获得经验值

### 装备机制
- **随机生成**: 基于等级和稀有度的属性范围
- **镶嵌孔**: 随机生成的镶嵌孔数量
- **符文系统**: 特定组合产生强大效果
- **装备评分**: 综合属性评估装备价值

### 经济系统
- **金币获取**: 击败敌人和出售装备
- **神秘商店**: 购买随机品质装备
- **装备交易**: 买卖装备获取金币
- **升级消耗**: 技能和装备升级成本

## 🔧 开发计划

### 已完成功能
- ✅ 基础项目架构
- ✅ 角色系统和职业设计
- ✅ 装备系统和随机生成
- ✅ 战斗系统基础逻辑
- ✅ UI界面和样式设计
- ✅ 状态管理和数据持久化

### 待开发功能
- 🔄 技能系统完善
- 🔄 符文和镶嵌系统
- 🔄 地图和关卡系统
- 🔄 成就和任务系统
- 🔄 音效和背景音乐
- 🔄 移动端适配

### 未来扩展
- 📋 多人在线功能
- 📋 装备交易市场
- 📋 公会系统
- 📋 PVP竞技场
- 📋 赛季排行榜

## 🎨 设计理念

游戏采用暗黑风格的视觉设计，以棕色和金色为主色调，营造出神秘而古典的氛围。界面布局参考了经典的RPG游戏，左侧为角色信息，中央为主要游戏区域，右侧为背包和控制面板。

## 📄 许可证

本项目仅供学习和演示使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进游戏！

---

**享受你的冒险之旅！** ⚔️🛡️🏹🧙‍♂️
