.achievement-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow: hidden;
}

.achievement-summary {
  background: rgba(30, 30, 30, 0.6);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #444;
}

.summary-stats {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: #ccc;
  font-size: 14px;
}

.stat-value {
  color: #FFD700;
  font-weight: bold;
  font-size: 14px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(20, 20, 20, 0.8);
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #444;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FFD700, #FFA500);
  transition: width 0.3s ease;
}

.achievement-controls {
  display: flex;
  gap: 20px;
  background: rgba(30, 30, 30, 0.6);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #444;
}

.filter-controls,
.sort-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-controls label,
.sort-controls label {
  color: #ccc;
  font-size: 12px;
  white-space: nowrap;
}

.control-select {
  background: rgba(20, 20, 20, 0.8);
  border: 1px solid #8B4513;
  border-radius: 4px;
  color: #e0e0e0;
  padding: 4px 8px;
  font-size: 12px;
  font-family: inherit;
}

.control-select:focus {
  outline: none;
  border-color: #FFD700;
}

.achievements-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.no-achievements {
  text-align: center;
  color: #888;
  font-style: italic;
  padding: 40px 20px;
}

.achievement-card {
  background: rgba(20, 20, 20, 0.8);
  border: 2px solid #444;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s ease;
  position: relative;
}

.achievement-card.completed {
  background: rgba(76, 175, 80, 0.1);
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.2);
}

.achievement-card.incomplete:hover {
  border-color: #8B4513;
  box-shadow: 0 0 10px rgba(139, 69, 19, 0.3);
}

.achievement-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}

.achievement-icon {
  font-size: 24px;
  width: 32px;
  text-align: center;
}

.achievement-info {
  flex: 1;
}

.achievement-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}

.achievement-rarity {
  font-size: 11px;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.completion-badge {
  background: #4CAF50;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

.achievement-description {
  color: #ccc;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 12px;
}

.achievement-progress {
  margin-bottom: 12px;
}

.progress-text {
  color: #e0e0e0;
  font-size: 12px;
  margin-bottom: 6px;
  font-weight: bold;
}

.achievement-rewards {
  margin-bottom: 15px;
}

.rewards-label {
  color: #FFD700;
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 6px;
}

.rewards-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.reward-item {
  background: rgba(255, 215, 0, 0.2);
  color: #FFD700;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.claim-button {
  width: 100%;
  padding: 8px;
  background: linear-gradient(145deg, #4CAF50, #45a049);
  border: none;
  border-radius: 4px;
  color: white;
  font-weight: bold;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.claim-button:hover {
  background: linear-gradient(145deg, #45a049, #4CAF50);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.claimed-badge {
  background: #666;
  color: #ccc;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
  text-align: center;
  border: 1px solid #555;
}

/* 滚动条样式 */
.achievements-list::-webkit-scrollbar {
  width: 8px;
}

.achievements-list::-webkit-scrollbar-track {
  background: rgba(20, 20, 20, 0.5);
  border-radius: 4px;
}

.achievements-list::-webkit-scrollbar-thumb {
  background: #8B4513;
  border-radius: 4px;
}

.achievements-list::-webkit-scrollbar-thumb:hover {
  background: #A0522D;
}

@media (max-width: 900px) {
  .achievement-controls {
    flex-direction: column;
    gap: 10px;
  }
  
  .achievement-header {
    flex-wrap: wrap;
  }
  
  .achievement-icon {
    font-size: 20px;
    width: 24px;
  }
  
  .rewards-list {
    flex-direction: column;
  }
}
