.app {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d1810 100%);
  color: #e0e0e0;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}

.game-layout {
  flex: 1;
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  gap: 10px;
  padding: 10px;
  height: calc(100vh - 120px);
}

.left-panel,
.center-panel,
.right-panel {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.left-panel {
  background: rgba(20, 20, 20, 0.9);
  border: 2px solid #8B4513;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 0 20px rgba(139, 69, 19, 0.3);
}

.center-panel {
  background: rgba(15, 15, 15, 0.95);
  border: 2px solid #8B4513;
  border-radius: 8px;
  box-shadow: 0 0 25px rgba(139, 69, 19, 0.4);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.center-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 30%, rgba(139, 69, 19, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255, 69, 0, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.right-panel {
  background: rgba(20, 20, 20, 0.9);
  border: 2px solid #8B4513;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 0 20px rgba(139, 69, 19, 0.3);
}

.game-status {
  height: 40px;
  background: rgba(10, 10, 10, 0.95);
  border-top: 2px solid #8B4513;
  display: flex;
  align-items: center;
  padding: 0 20px;
  gap: 30px;
  font-size: 12px;
  color: #ccc;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .game-layout {
    grid-template-columns: 250px 1fr 250px;
  }
}

@media (max-width: 900px) {
  .game-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }
  
  .left-panel,
  .right-panel {
    height: auto;
    max-height: 200px;
    overflow-y: auto;
  }
}

/* 滚动条样式 */
.left-panel::-webkit-scrollbar,
.right-panel::-webkit-scrollbar {
  width: 6px;
}

.left-panel::-webkit-scrollbar-track,
.right-panel::-webkit-scrollbar-track {
  background: rgba(20, 20, 20, 0.5);
  border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb {
  background: #8B4513;
  border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb:hover,
.right-panel::-webkit-scrollbar-thumb:hover {
  background: #A0522D;
}

/* 动画效果 */
.game-layout > * {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 面板标题样式 */
.panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 15px;
  text-align: center;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  border-bottom: 1px solid #8B4513;
  padding-bottom: 8px;
}

/* 通用按钮样式 */
.game-button {
  background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
  border: 1px solid #8B4513;
  color: #e0e0e0;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
  font-size: 12px;
}

.game-button:hover {
  background: linear-gradient(145deg, #4a4a4a, #3a3a3a);
  box-shadow: 0 0 10px rgba(139, 69, 19, 0.5);
  transform: translateY(-1px);
}

.game-button:active {
  transform: translateY(1px);
  box-shadow: 0 0 5px rgba(139, 69, 19, 0.3);
}

.game-button.primary {
  background: linear-gradient(145deg, #8B4513, #A0522D);
  color: #fff;
}

.game-button.primary:hover {
  background: linear-gradient(145deg, #A0522D, #CD853F);
  box-shadow: 0 0 15px rgba(139, 69, 19, 0.7);
}

/* 中央面板标签页 */
.center-panel-tabs {
  display: flex;
  background: rgba(20, 20, 20, 0.8);
  border-bottom: 2px solid #8B4513;
  margin: -2px -2px 0 -2px;
}

.tab-button {
  flex: 1;
  padding: 12px 20px;
  background: rgba(30, 30, 30, 0.8);
  border: none;
  border-right: 1px solid #8B4513;
  color: #ccc;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  font-size: 14px;
  font-weight: bold;
}

.tab-button:last-child {
  border-right: none;
}

.tab-button:hover {
  background: rgba(139, 69, 19, 0.3);
  color: #e0e0e0;
}

.tab-button.active {
  background: rgba(139, 69, 19, 0.6);
  color: #FFD700;
  box-shadow: inset 0 -3px 0 #FFD700;
}

.center-panel-content {
  flex: 1;
  padding: 20px;
  overflow: hidden;
}
