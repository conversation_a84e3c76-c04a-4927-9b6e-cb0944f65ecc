// 音效类型
export enum SoundType {
  CLICK = 'click',
  BATTLE_START = 'battle_start',
  BATTLE_HIT = 'battle_hit',
  BATTLE_WIN = 'battle_win',
  LEVEL_UP = 'level_up',
  ITEM_PICKUP = 'item_pickup',
  ITEM_EQUIP = 'item_equip',
  GOLD_PICKUP = 'gold_pickup',
  SKILL_LEARN = 'skill_learn',
  ACHIEVEMENT = 'achievement',
  ERROR = 'error',
  SUCCESS = 'success'
}

// 音效管理器类
class AudioManager {
  private audioContext: AudioContext | null = null;
  private sounds: Map<SoundType, AudioBuffer> = new Map();
  private enabled: boolean = true;
  private volume: number = 0.5;

  constructor() {
    this.initializeAudioContext();
    this.generateSounds();
  }

  private initializeAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.warn('Web Audio API not supported:', error);
    }
  }

  // 生成程序化音效
  private generateSounds() {
    if (!this.audioContext) return;

    // 点击音效
    this.generateTone(SoundType.CLICK, 800, 0.1, 'sine');
    
    // 战斗开始音效
    this.generateTone(SoundType.BATTLE_START, 400, 0.3, 'square');
    
    // 攻击音效
    this.generateNoise(SoundType.BATTLE_HIT, 0.1);
    
    // 胜利音效
    this.generateChord(SoundType.BATTLE_WIN, [523, 659, 784], 0.5);
    
    // 升级音效
    this.generateChord(SoundType.LEVEL_UP, [523, 659, 784, 1047], 0.8);
    
    // 物品拾取音效
    this.generateTone(SoundType.ITEM_PICKUP, 1000, 0.2, 'triangle');
    
    // 装备音效
    this.generateTone(SoundType.ITEM_EQUIP, 600, 0.3, 'sawtooth');
    
    // 金币音效
    this.generateTone(SoundType.GOLD_PICKUP, 1200, 0.15, 'sine');
    
    // 学习技能音效
    this.generateChord(SoundType.SKILL_LEARN, [440, 554, 659], 0.4);
    
    // 成就音效
    this.generateChord(SoundType.ACHIEVEMENT, [523, 659, 784, 1047, 1319], 1.0);
    
    // 错误音效
    this.generateTone(SoundType.ERROR, 200, 0.3, 'sawtooth');
    
    // 成功音效
    this.generateTone(SoundType.SUCCESS, 800, 0.4, 'triangle');
  }

  private generateTone(type: SoundType, frequency: number, duration: number, waveType: OscillatorType) {
    if (!this.audioContext) return;

    const sampleRate = this.audioContext.sampleRate;
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, length, sampleRate);
    const data = buffer.getChannelData(0);

    for (let i = 0; i < length; i++) {
      const t = i / sampleRate;
      let value = 0;

      switch (waveType) {
        case 'sine':
          value = Math.sin(2 * Math.PI * frequency * t);
          break;
        case 'square':
          value = Math.sin(2 * Math.PI * frequency * t) > 0 ? 1 : -1;
          break;
        case 'triangle':
          value = (2 / Math.PI) * Math.asin(Math.sin(2 * Math.PI * frequency * t));
          break;
        case 'sawtooth':
          value = 2 * (t * frequency - Math.floor(t * frequency + 0.5));
          break;
      }

      // 应用包络（淡入淡出）
      const envelope = this.getEnvelope(t, duration);
      data[i] = value * envelope;
    }

    this.sounds.set(type, buffer);
  }

  private generateNoise(type: SoundType, duration: number) {
    if (!this.audioContext) return;

    const sampleRate = this.audioContext.sampleRate;
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, length, sampleRate);
    const data = buffer.getChannelData(0);

    for (let i = 0; i < length; i++) {
      const t = i / sampleRate;
      const noise = (Math.random() * 2 - 1) * 0.3;
      const envelope = this.getEnvelope(t, duration);
      data[i] = noise * envelope;
    }

    this.sounds.set(type, buffer);
  }

  private generateChord(type: SoundType, frequencies: number[], duration: number) {
    if (!this.audioContext) return;

    const sampleRate = this.audioContext.sampleRate;
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, length, sampleRate);
    const data = buffer.getChannelData(0);

    for (let i = 0; i < length; i++) {
      const t = i / sampleRate;
      let value = 0;

      // 混合多个频率
      frequencies.forEach(freq => {
        value += Math.sin(2 * Math.PI * freq * t) / frequencies.length;
      });

      const envelope = this.getEnvelope(t, duration);
      data[i] = value * envelope;
    }

    this.sounds.set(type, buffer);
  }

  private getEnvelope(t: number, duration: number): number {
    const attackTime = 0.01;
    const releaseTime = 0.1;

    if (t < attackTime) {
      return t / attackTime;
    } else if (t > duration - releaseTime) {
      return (duration - t) / releaseTime;
    } else {
      return 1;
    }
  }

  // 播放音效
  public playSound(type: SoundType) {
    if (!this.enabled || !this.audioContext || !this.sounds.has(type)) {
      return;
    }

    try {
      const buffer = this.sounds.get(type)!;
      const source = this.audioContext.createBufferSource();
      const gainNode = this.audioContext.createGain();

      source.buffer = buffer;
      gainNode.gain.value = this.volume;

      source.connect(gainNode);
      gainNode.connect(this.audioContext.destination);

      source.start();
    } catch (error) {
      console.warn('Failed to play sound:', error);
    }
  }

  // 设置音效开关
  public setEnabled(enabled: boolean) {
    this.enabled = enabled;
  }

  // 设置音量
  public setVolume(volume: number) {
    this.volume = Math.max(0, Math.min(1, volume));
  }

  // 获取状态
  public isEnabled(): boolean {
    return this.enabled;
  }

  public getVolume(): number {
    return this.volume;
  }

  // 恢复音频上下文（用户交互后）
  public resumeAudioContext() {
    if (this.audioContext && this.audioContext.state === 'suspended') {
      this.audioContext.resume();
    }
  }
}

// 创建全局音效管理器实例
export const audioManager = new AudioManager();

// 便捷函数
export const playSound = (type: SoundType) => {
  audioManager.playSound(type);
};

export const setSoundEnabled = (enabled: boolean) => {
  audioManager.setEnabled(enabled);
};

export const setSoundVolume = (volume: number) => {
  audioManager.setVolume(volume);
};

export const isSoundEnabled = () => {
  return audioManager.isEnabled();
};

export const getSoundVolume = () => {
  return audioManager.getVolume();
};

// 初始化音频（需要用户交互）
export const initializeAudio = () => {
  audioManager.resumeAudioContext();
};
