import { 
  Character, 
  CharacterClass, 
  Stats, 
  Item, 
  ItemType, 
  ItemRarity, 
  StatType, 
  Area, 
  Enemy,
  ItemAffix 
} from '../types';

// 生成唯一ID
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

// 根据职业生成基础属性
export const getBaseStats = (characterClass: CharacterClass): Stats => {
  const baseStats: Record<CharacterClass, Stats> = {
    [CharacterClass.MAGE]: {
      strength: 10,
      dexterity: 15,
      intelligence: 25,
      vitality: 15,
      damage: 8,
      defense: 5,
      health: 50,
      mana: 100
    },
    [CharacterClass.PALADIN]: {
      strength: 20,
      dexterity: 15,
      intelligence: 15,
      vitality: 25,
      damage: 12,
      defense: 15,
      health: 100,
      mana: 50
    },
    [CharacterClass.RANGER]: {
      strength: 15,
      dexterity: 25,
      intelligence: 15,
      vitality: 20,
      damage: 15,
      defense: 8,
      health: 80,
      mana: 60
    },
    [CharacterClass.BARBARIAN]: {
      strength: 30,
      dexterity: 10,
      intelligence: 10,
      vitality: 25,
      damage: 18,
      defense: 12,
      health: 120,
      mana: 30
    }
  };

  return baseStats[characterClass];
};

// 生成角色
export const generateCharacter = (characterClass: CharacterClass, name: string): Character => {
  return {
    id: generateId(),
    name,
    class: characterClass,
    level: 1,
    experience: 0,
    experienceToNext: 100,
    stats: getBaseStats(characterClass),
    equipment: {},
    skills: [],
    activeSkills: [],
    isActive: true
  };
};

// 随机生成装备属性
export const generateRandomAffix = (): ItemAffix => {
  const statTypes = Object.values(StatType);
  const randomStat = statTypes[Math.floor(Math.random() * statTypes.length)];
  
  let value: number;
  let isPercentage = false;

  switch (randomStat) {
    case StatType.STRENGTH:
    case StatType.DEXTERITY:
    case StatType.INTELLIGENCE:
    case StatType.VITALITY:
      value = Math.floor(Math.random() * 20) + 1;
      break;
    case StatType.DAMAGE:
      value = Math.floor(Math.random() * 15) + 1;
      break;
    case StatType.DEFENSE:
      value = Math.floor(Math.random() * 10) + 1;
      break;
    case StatType.HEALTH:
      value = Math.floor(Math.random() * 50) + 10;
      break;
    case StatType.MANA:
      value = Math.floor(Math.random() * 30) + 5;
      break;
    default:
      value = Math.floor(Math.random() * 10) + 1;
  }

  return {
    type: randomStat,
    value,
    isPercentage
  };
};

// 生成随机装备
export const generateRandomItem = (level: number = 1, rarity?: ItemRarity): Item => {
  const itemTypes = Object.values(ItemType);
  const itemRarities = Object.values(ItemRarity);
  
  const randomType = itemTypes[Math.floor(Math.random() * itemTypes.length)];
  const randomRarity = rarity || itemRarities[Math.floor(Math.random() * itemRarities.length)];
  
  // 根据稀有度决定属性数量
  let affixCount: number;
  switch (randomRarity) {
    case ItemRarity.NORMAL:
      affixCount = 0;
      break;
    case ItemRarity.MAGIC:
      affixCount = Math.floor(Math.random() * 2) + 1;
      break;
    case ItemRarity.RARE:
      affixCount = Math.floor(Math.random() * 3) + 2;
      break;
    case ItemRarity.UNIQUE:
      affixCount = Math.floor(Math.random() * 4) + 3;
      break;
    case ItemRarity.LEGENDARY:
      affixCount = Math.floor(Math.random() * 5) + 4;
      break;
    default:
      affixCount = 1;
  }

  const stats: ItemAffix[] = [];
  for (let i = 0; i < affixCount; i++) {
    stats.push(generateRandomAffix());
  }

  // 生成装备名称
  const baseNames: Record<ItemType, string[]> = {
    [ItemType.WEAPON]: ['剑', '斧', '法杖', '弓', '匕首'],
    [ItemType.ARMOR]: ['皮甲', '锁甲', '板甲', '法袍'],
    [ItemType.HELMET]: ['头盔', '帽子', '王冠'],
    [ItemType.BOOTS]: ['靴子', '鞋子', '战靴'],
    [ItemType.GLOVES]: ['手套', '护手', '拳套'],
    [ItemType.RING]: ['戒指', '指环'],
    [ItemType.AMULET]: ['项链', '护身符', '吊坠']
  };

  const rarityPrefixes: Record<ItemRarity, string[]> = {
    [ItemRarity.NORMAL]: [''],
    [ItemRarity.MAGIC]: ['魔法的', '附魔的'],
    [ItemRarity.RARE]: ['稀有的', '精良的', '卓越的'],
    [ItemRarity.UNIQUE]: ['传奇的', '独特的', '神话的'],
    [ItemRarity.LEGENDARY]: ['史诗的', '传说的', '神器级']
  };

  const baseName = baseNames[randomType][Math.floor(Math.random() * baseNames[randomType].length)];
  const prefix = rarityPrefixes[randomRarity][Math.floor(Math.random() * rarityPrefixes[randomRarity].length)];
  const name = prefix ? `${prefix}${baseName}` : baseName;

  return {
    id: generateId(),
    name,
    type: randomType,
    rarity: randomRarity,
    level,
    stats,
    sockets: Math.random() < 0.3 ? Math.floor(Math.random() * 3) + 1 : 0,
    description: `等级 ${level} ${name}`
  };
};

// 生成敌人
export const generateEnemy = (level: number): Enemy => {
  const enemyNames = [
    '骷髅战士', '僵尸', '恶魔', '地狱犬', '暗影刺客',
    '火焰元素', '冰霜巨人', '毒蛛', '黑暗法师', '堕落骑士'
  ];

  const name = enemyNames[Math.floor(Math.random() * enemyNames.length)];
  const baseHealth = 30 + (level * 10);
  const baseDamage = 5 + (level * 2);
  const baseDefense = 2 + level;

  return {
    id: generateId(),
    name,
    level,
    health: baseHealth,
    maxHealth: baseHealth,
    damage: baseDamage,
    defense: baseDefense,
    experience: level * 10,
    lootTable: [
      { itemId: 'random', chance: 0.3 },
      { itemId: 'gold', chance: 0.8, quantity: level * 5 }
    ]
  };
};

// 生成起始区域
export const generateStartingArea = (): Area => {
  const enemies = Array.from({ length: 5 }, () => generateEnemy(1));
  
  return {
    id: 'starting_area',
    name: '被遗忘的墓地',
    level: 1,
    enemies,
    description: '一个古老的墓地，充满了不死生物的威胁。这里是你冒险的起点。',
    unlocked: true
  };
};

// 计算战斗伤害
export const calculateDamage = (attacker: Stats, defender: Stats): number => {
  const baseDamage = attacker.damage;
  const defense = defender.defense;
  const finalDamage = Math.max(1, baseDamage - defense);
  
  // 添加一些随机性
  const variance = 0.2;
  const randomFactor = 1 + (Math.random() - 0.5) * variance;
  
  return Math.floor(finalDamage * randomFactor);
};

// 格式化时间显示
export const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

// 格式化数字显示
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};
